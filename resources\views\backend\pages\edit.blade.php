<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Edit Page: ') . $page->title }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ $page->category ? route('pages.show', [$page->category->slug, $page->slug]) : route('pages.show.simple', $page->slug) }}" 
                   class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    View Page
                </a>
                <a href="{{ route('pages.manage') }}" 
                   class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    Back to Manage
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                    <span class="block sm:inline">{{ session('success') }}</span>
                </div>
            @endif

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <form action="{{ route('pages.update', $page) }}" method="POST" x-data="pageForm()">
                        @csrf
                        @method('PUT')

                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                            <!-- Main Content -->
                            <div class="lg:col-span-2 space-y-6">
                                <!-- Title -->
                                <div>
                                    <x-input-label for="title" :value="__('Title')" />
                                    <x-text-input id="title" name="title" type="text" class="mt-1 block w-full" 
                                                  :value="old('title', $page->title)" required autofocus 
                                                  x-model="title" @input="generateSlug" />
                                    <x-input-error class="mt-2" :messages="$errors->get('title')" />
                                </div>

                                <!-- Slug -->
                                <div>
                                    <x-input-label for="slug" :value="__('Slug')" />
                                    <x-text-input id="slug" name="slug" type="text" class="mt-1 block w-full" 
                                                  :value="old('slug', $page->slug)" x-model="slug" />
                                    <p class="mt-1 text-sm text-gray-500">URL-friendly version of the title.</p>
                                    <x-input-error class="mt-2" :messages="$errors->get('slug')" />
                                </div>

                                <!-- Content -->
                                <div>
                                    <x-input-label for="content" :value="__('Content')" />
                                    <x-quill-editor
                                        name="content"
                                        :value="old('content', $page->content)"
                                        placeholder="Write your page content here..."
                                        height="400px"
                                        required />
                                </div>

                                <!-- Meta Description -->
                                <div>
                                    <x-input-label for="meta_description" :value="__('Meta Description')" />
                                    <textarea id="meta_description" name="meta_description" rows="3" 
                                              class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                              maxlength="160">{{ old('meta_description', $page->meta_description) }}</textarea>
                                    <p class="mt-1 text-sm text-gray-500">Brief description for search engines (max 160 characters).</p>
                                    <x-input-error class="mt-2" :messages="$errors->get('meta_description')" />
                                </div>
                            </div>

                            <!-- Sidebar -->
                            <div class="space-y-6">
                                <!-- Page Info -->
                                <div class="bg-blue-50 p-4 rounded-lg">
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">Page Information</h3>
                                    <div class="space-y-2 text-sm text-gray-600">
                                        <div><strong>Created:</strong> {{ $page->created_at->format('M j, Y g:i A') }}</div>
                                        <div><strong>Updated:</strong> {{ $page->updated_at->format('M j, Y g:i A') }}</div>
                                        <div><strong>Views:</strong> {{ $page->views }}</div>
                                        <div><strong>Author:</strong> {{ $page->author->name }}</div>
                                    </div>
                                </div>

                                <!-- Publish Settings -->
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">Publish Settings</h3>
                                    
                                    <div class="space-y-4">
                                        <!-- Status -->
                                        <div>
                                            <label class="flex items-center">
                                                <input type="checkbox" name="status" value="1" 
                                                       class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                                                       {{ old('status', $page->status) ? 'checked' : '' }}>
                                                <span class="ml-2 text-sm text-gray-600">Published</span>
                                            </label>
                                        </div>

                                        <!-- Featured -->
                                        <div>
                                            <label class="flex items-center">
                                                <input type="checkbox" name="featured" value="1" 
                                                       class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                                                       {{ old('featured', $page->featured) ? 'checked' : '' }}>
                                                <span class="ml-2 text-sm text-gray-600">Mark as featured</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Category -->
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">Category</h3>
                                    <select name="category_id" 
                                            class="w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm">
                                        <option value="">Select Category</option>
                                        @foreach($categories as $category)
                                            <option value="{{ $category->id }}" 
                                                    {{ old('category_id', $page->category_id) == $category->id ? 'selected' : '' }}>
                                                {{ $category->category }}
                                            </option>
                                        @endforeach
                                    </select>
                                    <x-input-error class="mt-2" :messages="$errors->get('category_id')" />
                                </div>

                                <!-- Tags -->
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">Tags</h3>
                                    <div x-data="tagSelector()" class="space-y-2">
                                        <div class="relative">
                                            <input type="text" x-model="search" @input="searchTags" @keydown.enter.prevent="addTag"
                                                   placeholder="Search and select tags..."
                                                   class="w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm">
                                            
                                            <div x-show="showDropdown && filteredTags.length > 0" 
                                                 class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-40 overflow-y-auto">
                                                <template x-for="tag in filteredTags" :key="tag.id">
                                                    <div @click="selectTag(tag)" 
                                                         class="px-3 py-2 cursor-pointer hover:bg-gray-100 flex items-center justify-between">
                                                        <span x-text="tag.name"></span>
                                                        <span class="w-3 h-3 rounded-full" :style="`background-color: ${tag.color}`"></span>
                                                    </div>
                                                </template>
                                            </div>
                                        </div>

                                        <div class="flex flex-wrap gap-2">
                                            <template x-for="tag in selectedTags" :key="tag.id">
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                                                      :style="`background-color: ${tag.color}20; color: ${tag.color};`">
                                                    <span x-text="tag.name"></span>
                                                    <button type="button" @click="removeTag(tag.id)" class="ml-1 text-xs">×</button>
                                                    <input type="hidden" name="tags[]" :value="tag.id">
                                                </span>
                                            </template>
                                        </div>
                                    </div>
                                </div>

                                <!-- Actions -->
                                <div class="flex flex-col space-y-2">
                                    <x-primary-button type="submit">
                                        {{ __('Update Page') }}
                                    </x-primary-button>
                                    <x-secondary-button type="button" onclick="window.history.back()">
                                        {{ __('Cancel') }}
                                    </x-secondary-button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function pageForm() {
            return {
                title: '{{ old('title', $page->title) }}',
                slug: '{{ old('slug', $page->slug) }}',
                generateSlug() {
                    // Only auto-generate if slug is empty
                    if (this.title && !this.slug) {
                        this.slug = this.title.toLowerCase()
                            .replace(/[^a-z0-9\s-]/g, '')
                            .replace(/\s+/g, '-')
                            .replace(/-+/g, '-')
                            .trim('-');
                    }
                }
            }
        }

        function tagSelector() {
            return {
                search: '',
                showDropdown: false,
                selectedTags: @json($page->tags),
                filteredTags: [],
                availableTags: @json($tags),
                
                searchTags() {
                    if (this.search.length > 0) {
                        this.filteredTags = this.availableTags.filter(tag => 
                            tag.name.toLowerCase().includes(this.search.toLowerCase()) &&
                            !this.selectedTags.find(selected => selected.id === tag.id)
                        );
                        this.showDropdown = true;
                    } else {
                        this.showDropdown = false;
                    }
                },
                
                selectTag(tag) {
                    this.selectedTags.push(tag);
                    this.search = '';
                    this.showDropdown = false;
                },
                
                removeTag(tagId) {
                    this.selectedTags = this.selectedTags.filter(tag => tag.id !== tagId);
                },
                
                addTag() {
                    if (this.filteredTags.length > 0) {
                        this.selectTag(this.filteredTags[0]);
                    }
                }
            }
        }
    </script>
</x-app-layout>
