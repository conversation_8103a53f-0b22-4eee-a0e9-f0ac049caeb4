<?php

namespace App\Http\Controllers;

use App\Models\Page;
use App\Models\Category;
use App\Models\Tag;
use Illuminate\Http\Request;

class WebController extends Controller
{
    public function home()
    {
        $featuredPages = Page::with(['author', 'category', 'tags'])
                            ->published()
                            ->featured()
                            ->latest()
                            ->take(6)
                            ->get();

        $recentPages = Page::with(['author', 'category', 'tags'])
                          ->published()
                          ->latest()
                          ->take(8)
                          ->get();

        $categories = Category::active()
                             ->withCount(['pages' => function($query) {
                                 $query->published();
                             }])
                             ->orderBy('category')
                             ->get();

        return view('web.home', compact('featuredPages', 'recentPages', 'categories'));
    }

    public function pages()
    {
        $pages = Page::with(['author', 'category', 'tags'])
                    ->published()
                    ->latest()
                    ->paginate(12);

        return view('web.pages.index', compact('pages'));
    }

    public function pagesByCategory($categorySlug)
    {
        $category = Category::where('slug', $categorySlug)->firstOrFail();
        
        $pages = Page::with(['author', 'category', 'tags'])
                    ->where('category_id', $category->id)
                    ->published()
                    ->latest()
                    ->paginate(12);

        return view('web.pages.category', compact('pages', 'category'));
    }

    public function pagesByTag($tagSlug)
    {
        $tag = Tag::where('slug', $tagSlug)->firstOrFail();
        
        $pages = $tag->pages()
                    ->with(['author', 'category'])
                    ->published()
                    ->latest()
                    ->paginate(12);

        return view('web.pages.tag', compact('pages', 'tag'));
    }

    public function showPage($categorySlug, $pageSlug)
    {
        $category = Category::where('slug', $categorySlug)->firstOrFail();
        $page = Page::with(['author', 'category', 'tags'])
                   ->where('slug', $pageSlug)
                   ->where('category_id', $category->id)
                   ->published()
                   ->firstOrFail();

        // Increment views
        $page->increment('views');

        // Get related pages from same category
        $relatedPages = Page::with(['author', 'category'])
                           ->where('category_id', $category->id)
                           ->where('id', '!=', $page->id)
                           ->published()
                           ->latest()
                           ->take(4)
                           ->get();

        return view('web.pages.show', compact('page', 'category', 'relatedPages'));
    }

    public function showPageWithoutCategory($pageSlug)
    {
        $page = Page::with(['author', 'category', 'tags'])
                   ->where('slug', $pageSlug)
                   ->published()
                   ->firstOrFail();

        // Increment views
        $page->increment('views');

        // Get related pages
        $relatedPages = Page::with(['author', 'category'])
                           ->where('id', '!=', $page->id)
                           ->published()
                           ->latest()
                           ->take(4)
                           ->get();

        return view('web.pages.show', compact('page', 'relatedPages'));
    }

    public function about()
    {
        return view('web.about');
    }

    public function contact()
    {
        return view('web.contact');
    }

    public function services()
    {
        return view('web.services');
    }
}
